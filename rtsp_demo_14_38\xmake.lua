-- xmake.lua for RTSP demo with VS2022 tools 14.38.33130
set_project("rtsp_demo_14_38")
set_version("1.0.0")

-- 设置构建模式
add_rules("mode.debug", "mode.release")

-- 设置C++标准
set_languages("c++17")

-- 设置编译器工具链版本
set_toolchains("msvc", {version = "14.38.33130"})

-- 设置全局运行时库（修复vs_runtime警告）
set_runtimes("MD")

-- 检查是否有VCPKG环境
local has_vcpkg = os.getenv("VCPKG_ROOT")

-- 添加FFmpeg依赖
if has_vcpkg then
    print("Using vcpkg FFmpeg...")
    add_requires("vcpkg::ffmpeg[core,avcodec,avformat,avutil,swscale,avdevice]")
else
    -- 检查是否有预编译的FFmpeg
    if os.exists("deps/ffmpeg") then
        print("Using pre-compiled FFmpeg from deps/ffmpeg")
    else
        print("Warning: FFmpeg will be downloaded and compiled, this may take time...")
        print("Consider using vcpkg or pre-compiled FFmpeg for faster builds")
        print("You can also run: setup_ffmpeg_windows.bat")
        
        add_requires("ffmpeg", {
            configs = {
                shared = true,
                toolchains = "msvc",
                runtimes = "MD",  -- 使用runtimes替代vs_runtime
                gpl = true,
                version3 = true
            }
        })
    end
end

-- 定义目标
target("rtsp_demo")
    set_kind("binary")
    add_files("src/*.cpp")
    add_files("src/*.c")  -- 如果有C文件
    add_headerfiles("src/*.h")
    add_headerfiles("src/*.hpp")  -- 如果有hpp文件

    -- 添加包含目录
    add_includedirs("src")
    
    -- 添加FFmpeg包
    if has_vcpkg then
        add_packages("vcpkg::ffmpeg")
    elseif os.exists("deps/ffmpeg") then
        -- 使用预编译FFmpeg
        add_includedirs("deps/ffmpeg/include")
        add_linkdirs("deps/ffmpeg/lib")
        add_links("avcodec", "avformat", "avutil", "swscale", "avdevice")
    else
        add_packages("ffmpeg")
    end
    
    -- 设置输出目录
    set_targetdir("bin")
    set_objectdir("build/obj")
    
    -- 添加编译选项
    add_cxxflags("/W3")
    add_defines("_CRT_SECURE_NO_WARNINGS")
    add_defines("WIN32_LEAN_AND_MEAN")
    
    -- Windows系统链接库
    add_links("ws2_32", "secur32", "bcrypt", "winmm", "user32")
    
    -- 设置运行时库
    set_runtimes("MD")
    
    -- 调试配置
    if is_mode("debug") then
        add_defines("DEBUG", "_DEBUG")
        set_optimize("none")
        set_warnings("all")
        add_cxxflags("/Zi", "/Od")
    end
    
    -- 发布配置
    if is_mode("release") then
        add_defines("NDEBUG")
        set_optimize("fastest")
        add_cxxflags("/O2")
    end

-- 添加安装规则
target("install")
    set_kind("phony")
    on_build(function(target)
        -- 复制必要的DLL文件
        if os.exists("deps/ffmpeg/bin") then
            os.cp("deps/ffmpeg/bin/*.dll", "bin/")
        end
    end)