#pragma once

#include <string>
#include <memory>
#include <optional>
#include <chrono>
#include <functional>

extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/avutil.h>
#include <libavutil/imgutils.h>
#include <libswscale/swscale.h>
}

// 使用现代C++特性的RTSP客户端
class RTSPClient {
public:
    // 使用强类型枚举
    enum class ConnectionStatus {
        Disconnected,
        Connecting,
        Connected,
        Error
    };

    // 回调函数类型定义
    using ProgressCallback = std::function<void(int frameCount, std::chrono::seconds elapsed)>;
    using ErrorCallback = std::function<void(const std::string& error)>;

    RTSPClient();
    ~RTSPClient();

    // 禁用拷贝构造和赋值
    RTSPClient(const RTSPClient&) = delete;
    RTSPClient& operator=(const RTSPClient&) = delete;

    // 支持移动语义
    RTSPClient(RTSPClient&& other) noexcept;
    RTSPClient& operator=(RTSPClient&& other) noexcept;

    [[nodiscard]] bool initialize();
    [[nodiscard]] bool openStream(const std::string& rtspUrl);
    [[nodiscard]] bool saveToFile(const std::string& outputFile, 
                                  std::chrono::seconds duration = std::chrono::seconds{30});
    
    void setProgressCallback(ProgressCallback callback) { m_progressCallback = std::move(callback); }
    void setErrorCallback(ErrorCallback callback) { m_errorCallback = std::move(callback); }
    
    [[nodiscard]] ConnectionStatus getStatus() const noexcept { return m_status; }
    [[nodiscard]] std::optional<std::string> getLastError() const noexcept { return m_lastError; }
    
    void cleanup();

private:
    std::unique_ptr<AVFormatContext, void(*)(AVFormatContext*)> m_formatContext;
    std::unique_ptr<AVCodecContext, void(*)(AVCodecContext*)> m_codecContext;
    const AVCodec* m_codec;
    int m_videoStreamIndex;
    bool m_initialized;
    ConnectionStatus m_status;
    std::optional<std::string> m_lastError;
    
    ProgressCallback m_progressCallback;
    ErrorCallback m_errorCallback;

    [[nodiscard]] bool findVideoStream();
    [[nodiscard]] bool setupDecoder();
    void setError(const std::string& error);
    void reportProgress(int frameCount, std::chrono::seconds elapsed);
};
