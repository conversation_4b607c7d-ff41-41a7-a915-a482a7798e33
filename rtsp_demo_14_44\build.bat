@echo off
echo Building RTSP Demo with VS2022 Tools 14.44.35207 (C++20)...

REM 清理之前的构建
xmake clean

REM 配置项目，指定工具链版本
xmake config --toolchain=msvc --vs_toolset=14.44.35207 --mode=release

REM 编译项目
xmake build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful!
    echo Executable: bin\rtsp_demo.exe
    echo.
    echo Usage: bin\rtsp_demo.exe ^<rtsp_url^> ^<output_file^> [duration_seconds]
    echo Example: bin\rtsp_demo.exe rtsp://example.com:554/stream output.mp4 30
    echo.
    echo This version uses modern C++20 features:
    echo - std::format for string formatting
    echo - Smart pointers for memory management
    echo - Strong typed enums
    echo - std::optional for error handling
    echo - Progress and error callbacks
) else (
    echo.
    echo Build failed!
    exit /b 1
)

pause
