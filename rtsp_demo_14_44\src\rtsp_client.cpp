#include "rtsp_client.h"
#include <iostream>
#include <format>
#include <ranges>

// 自定义删除器
auto formatContextDeleter = [](AVFormatContext* ctx) {
    if (ctx) {
        avformat_close_input(&ctx);
    }
};

auto codecContextDeleter = [](AVCodecContext* ctx) {
    if (ctx) {
        avcodec_free_context(&ctx);
    }
};

RTSPClient::RTSPClient() 
    : m_formatContext(nullptr, formatContextDeleter)
    , m_codecContext(nullptr, codecContextDeleter)
    , m_codec(nullptr)
    , m_videoStreamIndex(-1)
    , m_initialized(false)
    , m_status(ConnectionStatus::Disconnected) {
}

RTSPClient::~RTSPClient() {
    cleanup();
}

RTSPClient::RTSPClient(RTSPClient&& other) noexcept
    : m_formatContext(std::move(other.m_formatContext))
    , m_codecContext(std::move(other.m_codecContext))
    , m_codec(other.m_codec)
    , m_videoStreamIndex(other.m_videoStreamIndex)
    , m_initialized(other.m_initialized)
    , m_status(other.m_status)
    , m_lastError(std::move(other.m_lastError))
    , m_progressCallback(std::move(other.m_progressCallback))
    , m_errorCallback(std::move(other.m_errorCallback)) {
    
    other.m_codec = nullptr;
    other.m_videoStreamIndex = -1;
    other.m_initialized = false;
    other.m_status = ConnectionStatus::Disconnected;
}

RTSPClient& RTSPClient::operator=(RTSPClient&& other) noexcept {
    if (this != &other) {
        cleanup();
        
        m_formatContext = std::move(other.m_formatContext);
        m_codecContext = std::move(other.m_codecContext);
        m_codec = other.m_codec;
        m_videoStreamIndex = other.m_videoStreamIndex;
        m_initialized = other.m_initialized;
        m_status = other.m_status;
        m_lastError = std::move(other.m_lastError);
        m_progressCallback = std::move(other.m_progressCallback);
        m_errorCallback = std::move(other.m_errorCallback);
        
        other.m_codec = nullptr;
        other.m_videoStreamIndex = -1;
        other.m_initialized = false;
        other.m_status = ConnectionStatus::Disconnected;
    }
    return *this;
}

bool RTSPClient::initialize() {
    if (m_initialized) {
        return true;
    }

    // 现代FFmpeg不需要av_register_all()
    if (const int ret = avformat_network_init(); ret < 0) {
        setError("Failed to initialize network");
        return false;
    }
    
    m_initialized = true;
    std::cout << "FFmpeg initialized successfully (Modern C++20 version)" << std::endl;
    return true;
}

bool RTSPClient::openStream(const std::string& rtspUrl) {
    if (!m_initialized) {
        setError("RTSPClient not initialized");
        return false;
    }

    m_status = ConnectionStatus::Connecting;

    // 分配格式上下文
    AVFormatContext* rawContext = avformat_alloc_context();
    if (!rawContext) {
        setError("Failed to allocate format context");
        m_status = ConnectionStatus::Error;
        return false;
    }
    
    m_formatContext.reset(rawContext);

    // 设置RTSP选项
    AVDictionary* options = nullptr;
    av_dict_set(&options, "rtsp_transport", "tcp", 0);
    av_dict_set(&options, "stimeout", "5000000", 0); // 5秒超时

    // 打开输入流
    AVFormatContext* tempContext = m_formatContext.release();
    const int ret = avformat_open_input(&tempContext, rtspUrl.c_str(), nullptr, &options);
    m_formatContext.reset(tempContext);
    av_dict_free(&options);
    
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        setError(std::format("Failed to open RTSP stream: {}", errbuf));
        m_status = ConnectionStatus::Error;
        return false;
    }

    // 获取流信息
    if (const int streamRet = avformat_find_stream_info(m_formatContext.get(), nullptr); streamRet < 0) {
        setError("Failed to find stream info");
        m_status = ConnectionStatus::Error;
        return false;
    }

    if (!findVideoStream() || !setupDecoder()) {
        m_status = ConnectionStatus::Error;
        return false;
    }

    m_status = ConnectionStatus::Connected;
    std::cout << std::format("RTSP stream opened successfully: {}", rtspUrl) << std::endl;
    return true;
}

bool RTSPClient::findVideoStream() {
    const auto streams = std::span(m_formatContext->streams, m_formatContext->nb_streams);
    
    for (const auto& [index, stream] : std::views::enumerate(streams)) {
        if (stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            m_videoStreamIndex = static_cast<int>(index);
            return true;
        }
    }

    setError("No video stream found");
    return false;
}

bool RTSPClient::setupDecoder() {
    const AVCodecParameters* codecpar = m_formatContext->streams[m_videoStreamIndex]->codecpar;
    
    // 查找解码器
    m_codec = avcodec_find_decoder(codecpar->codec_id);
    if (!m_codec) {
        setError("Codec not found");
        return false;
    }

    // 分配解码器上下文
    AVCodecContext* rawCodecContext = avcodec_alloc_context3(m_codec);
    if (!rawCodecContext) {
        setError("Failed to allocate codec context");
        return false;
    }
    
    m_codecContext.reset(rawCodecContext);

    // 复制参数
    if (const int ret = avcodec_parameters_to_context(m_codecContext.get(), codecpar); ret < 0) {
        setError("Failed to copy codec parameters");
        return false;
    }

    // 打开解码器
    if (const int ret = avcodec_open2(m_codecContext.get(), m_codec, nullptr); ret < 0) {
        setError("Failed to open codec");
        return false;
    }

    return true;
}

bool RTSPClient::saveToFile(const std::string& outputFile, std::chrono::seconds duration) {
    if (!m_formatContext || m_videoStreamIndex == -1) {
        setError("Stream not opened");
        return false;
    }

    // 创建输出格式上下文
    AVFormatContext* outputContext = nullptr;
    if (const int ret = avformat_alloc_output_context2(&outputContext, nullptr, nullptr, outputFile.c_str()); ret < 0) {
        setError("Failed to create output context");
        return false;
    }

    // RAII包装器
    const auto outputContextGuard = std::unique_ptr<AVFormatContext, void(*)(AVFormatContext*)>(
        outputContext, [](AVFormatContext* ctx) {
            if (ctx) {
                if (!(ctx->oformat->flags & AVFMT_NOFILE)) {
                    avio_closep(&ctx->pb);
                }
                avformat_free_context(ctx);
            }
        });

    // 添加视频流到输出
    AVStream* outStream = avformat_new_stream(outputContext, nullptr);
    if (!outStream) {
        setError("Failed to create output stream");
        return false;
    }

    // 复制流参数
    if (const int ret = avcodec_parameters_copy(outStream->codecpar, 
                                                m_formatContext->streams[m_videoStreamIndex]->codecpar); ret < 0) {
        setError("Failed to copy stream parameters");
        return false;
    }

    // 打开输出文件
    if (!(outputContext->oformat->flags & AVFMT_NOFILE)) {
        if (const int ret = avio_open(&outputContext->pb, outputFile.c_str(), AVIO_FLAG_WRITE); ret < 0) {
            setError("Failed to open output file");
            return false;
        }
    }

    // 写入文件头
    if (const int ret = avformat_write_header(outputContext, nullptr); ret < 0) {
        setError("Failed to write header");
        return false;
    }

    std::cout << std::format("Starting to save video to: {}", outputFile) << std::endl;
    std::cout << std::format("Duration: {} seconds", duration.count()) << std::endl;

    AVPacket packet;
    av_init_packet(&packet);
    
    const auto startTime = std::chrono::steady_clock::now();
    int frameCount = 0;

    while (true) {
        const auto currentTime = std::chrono::steady_clock::now();
        const auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(currentTime - startTime);
        
        if (elapsed >= duration) {
            std::cout << "Recording duration reached" << std::endl;
            break;
        }

        const int ret = av_read_frame(m_formatContext.get(), &packet);
        if (ret < 0) {
            if (ret == AVERROR_EOF) {
                std::cout << "End of stream reached" << std::endl;
            } else {
                setError("Error reading frame");
            }
            break;
        }

        if (packet.stream_index == m_videoStreamIndex) {
            // 重新计算时间戳
            packet.stream_index = 0;
            av_packet_rescale_ts(&packet, 
                                m_formatContext->streams[m_videoStreamIndex]->time_base,
                                outStream->time_base);

            if (const int writeRet = av_interleaved_write_frame(outputContext, &packet); writeRet < 0) {
                setError("Error writing frame");
                av_packet_unref(&packet);
                break;
            }
            frameCount++;
            
            // 报告进度
            if (frameCount % 30 == 0) {
                reportProgress(frameCount, elapsed);
            }
        }

        av_packet_unref(&packet);
    }

    // 写入文件尾
    av_write_trailer(outputContext);

    std::cout << std::format("Video saved successfully. Total frames: {}", frameCount) << std::endl;
    return true;
}

void RTSPClient::setError(const std::string& error) {
    m_lastError = error;
    if (m_errorCallback) {
        m_errorCallback(error);
    }
    std::cerr << "Error: " << error << std::endl;
}

void RTSPClient::reportProgress(int frameCount, std::chrono::seconds elapsed) {
    if (m_progressCallback) {
        m_progressCallback(frameCount, elapsed);
    } else {
        std::cout << std::format("Frames written: {}, Elapsed: {}s", frameCount, elapsed.count()) << std::endl;
    }
}

void RTSPClient::cleanup() {
    m_codecContext.reset();
    m_formatContext.reset();
    
    if (m_initialized) {
        avformat_network_deinit();
        m_initialized = false;
    }
    
    m_status = ConnectionStatus::Disconnected;
    m_lastError.reset();
}
