@echo off
echo Building RTSP Demo - Pure Windows Build (No MSYS2)...

REM 检查是否已下载预编译FFmpeg
if not exist "..\ffmpeg_prebuilt\ffmpeg_libs" (
    echo.
    echo FFmpeg libraries not found!
    echo Please run setup_ffmpeg_windows.bat first to download pre-compiled FFmpeg.
    echo.
    echo This will avoid MSYS2 dependency and use pure Windows libraries.
    echo.
    pause
    exit /b 1
)

echo.
echo Using pre-compiled FFmpeg libraries (No MSYS2 required)
echo FFmpeg path: ..\ffmpeg_prebuilt\ffmpeg_libs

REM 清理之前的构建
xmake clean

REM 配置项目
xmake config --toolchain=msvc --vs_toolset=14.44.35207 --mode=release

REM 编译项目
xmake build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Build successful!
    echo Executable: bin\rtsp_demo_native.exe
    echo.
    echo This build uses:
    echo - Pre-compiled FFmpeg libraries
    echo - Pure Windows/MSVC toolchain
    echo - No MSYS2 or Unix emulation
    echo.
    echo Usage: bin\rtsp_demo_native.exe ^<rtsp_url^> ^<output_file^> [duration_seconds]
    echo Example: bin\rtsp_demo_native.exe rtsp://example.com:554/stream output.mp4 30
) else (
    echo.
    echo ❌ Build failed!
    echo Make sure you have:
    echo 1. Run setup_ffmpeg_windows.bat
    echo 2. VS2022 with MSVC 14.44.35207 installed
    echo 3. xmake properly configured
    exit /b 1
)

pause
