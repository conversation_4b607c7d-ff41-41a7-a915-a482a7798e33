#include "rtsp_capture.h"
#include <iostream>
#include <format>
#include <iomanip>
#include <sstream>

namespace rtsp_demo {

RTSPCapture::RTSPCapture()
    : m_status(CaptureStatus::Disconnected)
    , m_interval(std::chrono::seconds{30})
    , m_shouldStop(false)
    , m_screenshotCount(0) {
}

RTSPCapture::~RTSPCapture() {
    cleanup();
}

RTSPCapture::RTSPCapture(RTSPCapture&& other) noexcept
    : m_capture(std::move(other.m_capture))
    , m_status(other.m_status.load())
    , m_lastError(std::move(other.m_lastError))
    , m_outputDir(std::move(other.m_outputDir))
    , m_interval(other.m_interval)
    , m_shouldStop(other.m_shouldStop.load())
    , m_screenshotCount(other.m_screenshotCount.load())
    , m_startTime(other.m_startTime)
    , m_captureThread(std::move(other.m_captureThread))
    , m_screenshotCallback(std::move(other.m_screenshotCallback))
    , m_errorCallback(std::move(other.m_errorCallback))
    , m_statusCallback(std::move(other.m_statusCallback)) {
    
    other.m_status = CaptureStatus::Disconnected;
    other.m_shouldStop = false;
    other.m_screenshotCount = 0;
}

RTSPCapture& RTSPCapture::operator=(RTSPCapture&& other) noexcept {
    if (this != &other) {
        cleanup();
        
        m_capture = std::move(other.m_capture);
        m_status = other.m_status.load();
        m_lastError = std::move(other.m_lastError);
        m_outputDir = std::move(other.m_outputDir);
        m_interval = other.m_interval;
        m_shouldStop = other.m_shouldStop.load();
        m_screenshotCount = other.m_screenshotCount.load();
        m_startTime = other.m_startTime;
        m_captureThread = std::move(other.m_captureThread);
        m_screenshotCallback = std::move(other.m_screenshotCallback);
        m_errorCallback = std::move(other.m_errorCallback);
        m_statusCallback = std::move(other.m_statusCallback);
        
        other.m_status = CaptureStatus::Disconnected;
        other.m_shouldStop = false;
        other.m_screenshotCount = 0;
    }
    return *this;
}

bool RTSPCapture::initialize(const std::string& rtspUrl) {
    setStatus(CaptureStatus::Connecting);
    
    std::cout << std::format("Connecting to RTSP stream: {}", rtspUrl) << std::endl;
    
    // 设置OpenCV的一些参数
    m_capture.set(cv::CAP_PROP_BUFFERSIZE, 1); // 减少缓冲区大小，降低延迟
    
    if (!m_capture.open(rtspUrl)) {
        setError(std::format("Failed to open RTSP stream: {}", rtspUrl));
        setStatus(CaptureStatus::Error);
        return false;
    }
    
    // 检查是否成功连接
    cv::Mat testFrame;
    if (!m_capture.read(testFrame) || testFrame.empty()) {
        setError("Failed to read frame from RTSP stream");
        setStatus(CaptureStatus::Error);
        return false;
    }
    
    setStatus(CaptureStatus::Connected);
    std::cout << std::format("Successfully connected to RTSP stream") << std::endl;
    std::cout << std::format("Frame size: {}x{}", testFrame.cols, testFrame.rows) << std::endl;
    
    return true;
}

bool RTSPCapture::startCapture(const std::filesystem::path& outputDir, std::chrono::seconds interval) {
    if (m_status != CaptureStatus::Connected) {
        setError("RTSP stream not connected");
        return false;
    }
    
    m_outputDir = outputDir;
    m_interval = interval;
    m_shouldStop = false;
    m_screenshotCount = 0;
    m_startTime = std::chrono::system_clock::now();
    
    // 创建输出目录
    std::error_code ec;
    if (!std::filesystem::exists(m_outputDir, ec)) {
        if (!std::filesystem::create_directories(m_outputDir, ec)) {
            setError(std::format("Failed to create output directory: {}", m_outputDir.string()));
            return false;
        }
    }
    
    // 启动捕获线程
    m_captureThread = std::make_unique<std::thread>(&RTSPCapture::captureLoop, this);
    
    setStatus(CaptureStatus::Capturing);
    std::cout << std::format("Started capturing screenshots every {}s to: {}", 
                            interval.count(), m_outputDir.string()) << std::endl;
    
    return true;
}

void RTSPCapture::stopCapture() {
    if (m_status == CaptureStatus::Capturing) {
        m_shouldStop = true;
        
        if (m_captureThread && m_captureThread->joinable()) {
            m_captureThread->join();
        }
        
        setStatus(CaptureStatus::Stopped);
        std::cout << std::format("Capture stopped. Total screenshots: {}", m_screenshotCount.load()) << std::endl;
    }
}

bool RTSPCapture::takeScreenshot(const std::filesystem::path& filename) {
    if (m_status != CaptureStatus::Connected && m_status != CaptureStatus::Capturing) {
        setError("RTSP stream not connected");
        return false;
    }
    
    cv::Mat frame;
    if (!m_capture.read(frame) || frame.empty()) {
        setError("Failed to read frame for screenshot");
        return false;
    }
    
    std::string actualFilename = filename.empty() ? generateFilename() : filename.string();
    
    if (saveFrame(frame, actualFilename)) {
        ScreenshotInfo info{
            .filename = actualFilename,
            .timestamp = std::chrono::system_clock::now(),
            .frameSize = cv::Size(frame.cols, frame.rows),
            .success = true
        };
        
        if (m_screenshotCallback) {
            m_screenshotCallback(info);
        }
        
        m_screenshotCount++;
        return true;
    }
    
    return false;
}

void RTSPCapture::captureLoop() {
    auto lastScreenshotTime = std::chrono::steady_clock::now();
    
    while (!m_shouldStop) {
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(currentTime - lastScreenshotTime);
        
        if (elapsed >= m_interval) {
            cv::Mat frame;
            if (m_capture.read(frame) && !frame.empty()) {
                std::string filename = generateFilename();
                
                if (saveFrame(frame, filename)) {
                    ScreenshotInfo info{
                        .filename = filename,
                        .timestamp = std::chrono::system_clock::now(),
                        .frameSize = cv::Size(frame.cols, frame.rows),
                        .success = true
                    };
                    
                    if (m_screenshotCallback) {
                        m_screenshotCallback(info);
                    } else {
                        std::cout << std::format("Screenshot saved: {} ({}x{})", 
                                                filename, frame.cols, frame.rows) << std::endl;
                    }
                    
                    m_screenshotCount++;
                    lastScreenshotTime = currentTime;
                } else {
                    setError(std::format("Failed to save screenshot: {}", filename));
                }
            } else {
                setError("Failed to read frame from RTSP stream");
                // 尝试重新连接
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }
        
        // 短暂休眠，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void RTSPCapture::setStatus(CaptureStatus status) {
    m_status = status;
    if (m_statusCallback) {
        m_statusCallback(status);
    }
}

void RTSPCapture::setError(const std::string& error) {
    m_lastError = error;
    if (m_errorCallback) {
        m_errorCallback(error);
    }
    std::cerr << std::format("Error: {}", error) << std::endl;
}

std::string RTSPCapture::generateFilename() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
    ss << std::format("_{:03d}", ms.count());
    
    auto filepath = m_outputDir / std::format("screenshot_{}.jpg", ss.str());
    return filepath.string();
}

bool RTSPCapture::saveFrame(const cv::Mat& frame, const std::string& filename) {
    try {
        // 设置JPEG压缩参数
        std::vector<int> compression_params;
        compression_params.push_back(cv::IMWRITE_JPEG_QUALITY);
        compression_params.push_back(95); // 高质量
        
        return cv::imwrite(filename, frame, compression_params);
    } catch (const cv::Exception& e) {
        setError(std::format("OpenCV error saving image: {}", e.what()));
        return false;
    } catch (const std::exception& e) {
        setError(std::format("Error saving image: {}", e.what()));
        return false;
    }
}

void RTSPCapture::cleanup() {
    stopCapture();
    
    if (m_capture.isOpened()) {
        m_capture.release();
    }
    
    m_captureThread.reset();
    setStatus(CaptureStatus::Disconnected);
}

} // namespace rtsp_demo
