{
    ["find_program_msvc_arch_x64_plat_windows_version_14.44.35207_checktoolcxx"] = {
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    ["find_program_msvc_arch_x64_plat_windows_version_14.44.35207_checktoolld"] = {
        ["link.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    ["detect.sdks.find_vcpkgdir"] = false,
    find_package_windows_x64_fetch_package_system = {
        ffmpeg_9598f6f791f44c8cb248c15cb5c55a5d_release_external = false
    },
    find_package_windows_x64_fetch_package_xmake = {
        ["xmake::ffmpeg_9598f6f791f44c8cb248c15cb5c55a5d_release_7.1_external"] = false
    },
    ["find_program_msvc_arch_x64_plat_windows_version_14.44.35207_checktoolas"] = {
        ["ml64.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\ml64.exe]]
    },
    ["find_program_msvc_arch_x64_plat_windows_version_14.44.35207_checktoolcc"] = {
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    find_programver = {
        ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe"] = "19.44.35214"
    },
    find_program_fetch_package_xmake = {
        nasm = false,
        msys2 = false,
        ["msys2-base"] = false
    },
    ["find_program_msvc_arch_x64_plat_windows_version_14.44.35207_checktoolsh"] = {
        ["link.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    find_program = {
        git = [[C:\Program Files\Git\cmd\git.exe]],
        ["7z"] = [[C:\Users\<USER>\scoop\apps\xmake\current\winenv\bin\7z]],
        nim = false,
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]],
        curl = [[C:\Users\<USER>\scoop\apps\xmake\current\winenv\bin\curl]],
        make = [[C:\Users\<USER>\AppData\Local\.xmake\packages\m\msys2-base\2024.01.13\a77fa58bdf6d49bbbfa0c92a5cf82862\usr\bin\make.exe]],
        gzip = false
    },
    ["find_program_msvc_arch_x64_plat_windows_version_14.44.35207_checktoolar"] = {
        ["link.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    }
}