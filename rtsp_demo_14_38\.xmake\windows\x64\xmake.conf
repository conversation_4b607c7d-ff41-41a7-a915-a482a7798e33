{
    __toolchains_windows_x64 = {
        "msvc",
        "yasm",
        "nasm",
        "cuda",
        "rust",
        "swift",
        "go",
        "gfortran",
        "fpc"
    },
    __toolchains_windows_x64_host = {
        "msvc",
        "yasm",
        "nasm",
        "cuda",
        "rust",
        "swift",
        "go",
        "gfortran",
        "fpc"
    },
    arch = "x64",
    builddir = "build",
    ccache = true,
    host = "windows",
    kind = "static",
    mode = "release",
    ndk_stdcxx = true,
    plat = "windows",
    vs = "2022"
}