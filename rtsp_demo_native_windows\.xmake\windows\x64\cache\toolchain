{
    ["tool_package_table: 00000246A212BE60_windows_x64_sh"] = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]],
        toolname = "link",
        toolchain_info = {
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows_version_14.44.35207",
            arch = "x64",
            name = "msvc"
        }
    },
    ["tool_package_table: 00000246A212BE60_windows_x64_as"] = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\ml64.exe]],
        toolname = "ml64",
        toolchain_info = {
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows_version_14.44.35207",
            arch = "x64",
            name = "msvc"
        }
    },
    rust_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64",
        __global = true
    },
    go_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64",
        __global = true
    },
    nim_arch_x64_plat_windows = {
        plat = "windows",
        __checked = false,
        arch = "x64",
        __global = true
    },
    yasm_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64",
        __global = true
    },
    cuda_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64",
        __global = true
    },
    ["tool_package_table: 00000246A212BE60_windows_x64_ar"] = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]],
        toolname = "link",
        toolchain_info = {
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows_version_14.44.35207",
            arch = "x64",
            name = "msvc"
        }
    },
    ["tool_package_table: 00000246A212BE60_windows_x64_ld"] = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]],
        toolname = "link",
        toolchain_info = {
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows_version_14.44.35207",
            arch = "x64",
            name = "msvc"
        }
    },
    msvc_arch_x64_plat_windows = {
        __global = true,
        arch = "x64",
        vs_toolset = "14.44.35207",
        __checked = "2022",
        plat = "windows",
        vcvars = {
            UniversalCRTSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]],
            VSCMD_ARG_HOST_ARCH = "x64",
            LIB = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64;C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64;C:\Program Files (x86)\Windows Kits\10\\lib\10.0.26100.0\\um\x64]],
            VisualStudioVersion = "17.0",
            WindowsSdkVerBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\]],
            VCToolsRedistDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Redist\MSVC\14.44.35112\]],
            VSCMD_VER = "17.14.11",
            VCInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\]],
            ExtensionSdkDir = [[C:\Program Files (x86)\Microsoft SDKs\Windows Kits\10\ExtensionSDKs]],
            VCToolsVersion = "14.44.35207",
            VSCMD_ARG_app_plat = "Desktop",
            WindowsSDKVersion = "10.0.26100.0",
            INCLUDE = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt]],
            VS170COMNTOOLS = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\]],
            UCRTVersion = "10.0.26100.0",
            WindowsSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]],
            WindowsSdkBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\]],
            LIBPATH = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x86\store\references;C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.26100.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0;C:\Windows\Microsoft.NET\Framework64\v4.0.30319]],
            WindowsLibPath = [[C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.26100.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0]],
            PATH = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\VCPackages;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TeamFoundation\Team Explorer;C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\bin\Roslyn;C:\Program Files\Microsoft Visual Studio\2022\Community\Team Tools\DiagnosticsHub\Collector;C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\\x64;C:\Program Files (x86)\Windows Kits\10\bin\\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\;D:\miniconda;D:\miniconda\Library\mingw-w64\bin;D:\miniconda\Library\usr\bin;D:\miniconda\Library\bin;D:\miniconda\Scripts;D:\miniconda\bin;D:\miniconda\condabin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\libnvvp;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2023.1.0;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin;C:\Program Files\Git\cmd;d:\cursor\resources\app\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;D:\PostgreSQL\16\bin;G:\cmake-3.31.6-windows-x86_64\bin;F:\TensorRT-*******.Windows10.x86_64.cuda-12.0\TensorRT-*******\lib;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;d:\Trae\bin;D:\miniconda;D:\miniconda\Library\mingw-w64\bin;D:\miniconda\Library\usr\bin;D:\miniconda\Library\bin;D:\miniconda\Scripts;C:\Users\<USER>\.local\bin;C:\Users\<USER>\scoop\apps\nodejs\current\bin;C:\Users\<USER>\scoop\apps\nodejs\current;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\cursor\resources\app\bin;D:\Microsoft VS Code\bin;D:\PyCharm 2025.1\bin;D:\CLion 2025.1\bin;D:\DataGrip 2025.1\bin;D:\Kiro\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\ajeetdsouza.zoxide_Microsoft.Winget.Source_8wekyb3d8bbwe;.;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\Linux\bin\ConnectionManagerExe;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\vcpkg]],
            VSInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\]],
            VCToolsInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\]],
            VCIDEInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\]],
            VSCMD_ARG_TGT_ARCH = "x64",
            DevEnvdir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\]]
        },
        vs_sdkver = "10.0.26100.0",
        vs = "2022",
        vcarchs = {
            "arm",
            "arm64",
            "arm64ec",
            "x64",
            "x86"
        }
    },
    swift_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64",
        __global = true
    },
    ["msvc_arch_x64_plat_windows_version_14.44.35207"] = {
        version = "14.44.35207",
        arch = "x64",
        vs_toolset = "14.44.35207",
        __checked = "2022",
        vcvars = ref("msvc_arch_x64_plat_windows", "vcvars"),
        plat = "windows",
        vs_sdkver = "10.0.26100.0",
        vs = "2022",
        vcarchs = {
            "arm",
            "arm64",
            "arm64ec",
            "x64",
            "x86"
        }
    },
    gfortran_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64",
        __global = true
    },
    fpc_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64",
        __global = true
    },
    nasm_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64",
        __global = true
    },
    ["tool_package_table: 00000246A212BE60_windows_x64_cc"] = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]],
        toolname = "cl",
        toolchain_info = {
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows_version_14.44.35207",
            arch = "x64",
            name = "msvc"
        }
    },
    ["tool_package_table: 00000246A212BE60_windows_x64_cxx"] = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]],
        toolname = "cl",
        toolchain_info = {
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows_version_14.44.35207",
            arch = "x64",
            name = "msvc"
        }
    }
}