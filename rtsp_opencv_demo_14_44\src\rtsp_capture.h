#pragma once

#include <opencv2/opencv.hpp>
#include <string>
#include <chrono>
#include <functional>
#include <optional>
#include <filesystem>
#include <atomic>
#include <thread>
#include <memory>

namespace rtsp_demo {

// 使用现代C++特性的RTSP截图类
class RTSPCapture {
public:
    // 强类型枚举
    enum class CaptureStatus {
        Disconnected,
        Connecting,
        Connected,
        Capturing,
        Error,
        Stopped
    };

    // 截图信息结构
    struct ScreenshotInfo {
        std::string filename;
        std::chrono::system_clock::time_point timestamp;
        cv::Size frameSize;
        bool success;
    };

    // 回调函数类型定义
    using ScreenshotCallback = std::function<void(const ScreenshotInfo& info)>;
    using ErrorCallback = std::function<void(const std::string& error)>;
    using StatusCallback = std::function<void(CaptureStatus status)>;

    RTSPCapture();
    ~RTSPCapture();

    // 禁用拷贝构造和赋值
    RTSPCapture(const RTSPCapture&) = delete;
    RTSPCapture& operator=(const RTSPCapture&) = delete;

    // 支持移动语义
    RTSPCapture(RTSPCapture&& other) noexcept;
    RTSPCapture& operator=(RTSPCapture&& other) noexcept;

    // 主要接口
    [[nodiscard]] bool initialize(const std::string& rtspUrl);
    [[nodiscard]] bool startCapture(const std::filesystem::path& outputDir = "screenshots",
                                    std::chrono::seconds interval = std::chrono::seconds{30});
    void stopCapture();
    
    // 设置回调函数
    void setScreenshotCallback(ScreenshotCallback callback) { m_screenshotCallback = std::move(callback); }
    void setErrorCallback(ErrorCallback callback) { m_errorCallback = std::move(callback); }
    void setStatusCallback(StatusCallback callback) { m_statusCallback = std::move(callback); }
    
    // 状态查询
    [[nodiscard]] CaptureStatus getStatus() const noexcept { return m_status; }
    [[nodiscard]] std::optional<std::string> getLastError() const noexcept { return m_lastError; }
    [[nodiscard]] size_t getScreenshotCount() const noexcept { return m_screenshotCount; }
    [[nodiscard]] std::chrono::system_clock::time_point getStartTime() const noexcept { return m_startTime; }

    // 手动截图
    [[nodiscard]] bool takeScreenshot(const std::filesystem::path& filename = "");

private:
    cv::VideoCapture m_capture;
    std::atomic<CaptureStatus> m_status;
    std::optional<std::string> m_lastError;
    std::filesystem::path m_outputDir;
    std::chrono::seconds m_interval;
    std::atomic<bool> m_shouldStop;
    std::atomic<size_t> m_screenshotCount;
    std::chrono::system_clock::time_point m_startTime;
    
    std::unique_ptr<std::thread> m_captureThread;
    
    // 回调函数
    ScreenshotCallback m_screenshotCallback;
    ErrorCallback m_errorCallback;
    StatusCallback m_statusCallback;

    // 私有方法
    void captureLoop();
    void setStatus(CaptureStatus status);
    void setError(const std::string& error);
    [[nodiscard]] std::string generateFilename() const;
    [[nodiscard]] bool saveFrame(const cv::Mat& frame, const std::string& filename);
    void cleanup();
};

} // namespace rtsp_demo
