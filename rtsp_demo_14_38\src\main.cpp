#include <iostream>
#include <string>
#include "rtsp_client.h"

int main(int argc, char* argv[]) {
    std::cout << "=== RTSP Video Recorder Demo (VS2022 Tools 14.38.33130) ===" << std::endl;
    
    std::string rtspUrl;
    std::string outputFile;
    int duration = 30;

    // 检查命令行参数
    if (argc >= 2) {
        rtspUrl = argv[1];
    } else {
        std::cout << "Enter RTSP URL: ";
        std::getline(std::cin, rtspUrl);
    }

    if (argc >= 3) {
        outputFile = argv[2];
    } else {
        std::cout << "Enter output file name (e.g., output.mp4): ";
        std::getline(std::cin, outputFile);
    }

    if (argc >= 4) {
        duration = std::stoi(argv[3]);
    } else {
        std::cout << "Enter recording duration in seconds (default 30): ";
        std::string durationStr;
        std::getline(std::cin, durationStr);
        if (!durationStr.empty()) {
            duration = std::stoi(durationStr);
        }
    }

    // 创建RTSP客户端
    RTSPClient client;
    
    // 初始化
    if (!client.initialize()) {
        std::cerr << "Failed to initialize RTSP client" << std::endl;
        return -1;
    }

    // 打开RTSP流
    std::cout << "Connecting to RTSP stream: " << rtspUrl << std::endl;
    if (!client.openStream(rtspUrl)) {
        std::cerr << "Failed to open RTSP stream" << std::endl;
        return -1;
    }

    // 保存视频到文件
    std::cout << "Starting video recording..." << std::endl;
    if (!client.saveToFile(outputFile, duration)) {
        std::cerr << "Failed to save video" << std::endl;
        return -1;
    }

    std::cout << "Video recording completed successfully!" << std::endl;
    std::cout << "Output file: " << outputFile << std::endl;
    
    return 0;
}
