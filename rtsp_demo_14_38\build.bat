@echo off
setlocal enabledelayedexpansion

echo ================================================
echo Setting up FFmpeg for Windows (VS2022/MSVC)
echo ================================================

:: 检查是否已经存在FFmpeg
if exist "deps\ffmpeg" (
    echo FFmpeg already exists in deps\ffmpeg
    choice /C YN /M "Do you want to re-download FFmpeg"
    if errorlevel 2 goto :end
    rmdir /s /q "deps\ffmpeg"
)

:: 创建依赖目录
if not exist "deps" mkdir deps
cd deps

echo.
echo Downloading pre-compiled FFmpeg (shared libraries)...
echo This may take a few minutes depending on your connection...

:: 下载最新的FFmpeg共享库版本
set FFMPEG_URL=https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl-shared.zip
set FFMPEG_ZIP=ffmpeg-shared.zip

:: 使用PowerShell下载（更可靠）
powershell -Command "try { Invoke-WebRequest -Uri '%FFMPEG_URL%' -OutFile '%FFMPEG_ZIP%' -UseBasicParsing; Write-Host 'Download completed successfully' } catch { Write-Host 'Download failed:' $_.Exception.Message; exit 1 }"

if not exist "%FFMPEG_ZIP%" (
    echo ERROR: Failed to download FFmpeg!
    echo Please check your internet connection and try again.
    goto :error
)

echo.
echo Extracting FFmpeg...
powershell -Command "try { Expand-Archive -Path '%FFMPEG_ZIP%' -DestinationPath '.' -Force; Write-Host 'Extraction completed' } catch { Write-Host 'Extraction failed:' $_.Exception.Message; exit 1 }"

:: 找到解压后的目录并重命名
for /d %%i in (ffmpeg-master-*) do (
    if exist "%%i" (
        echo Renaming %%i to ffmpeg...
        move "%%i" ffmpeg
        goto :rename_done
    )
)

echo ERROR: Could not find extracted FFmpeg directory!
goto :error

:rename_done
:: 验证FFmpeg结构
if not exist "ffmpeg\bin" (
    echo ERROR: FFmpeg bin directory not found!
    goto :error
)

if not exist "ffmpeg\lib" (
    echo ERROR: FFmpeg lib directory not found!
    goto :error
)

if not exist "ffmpeg\include" (
    echo ERROR: FFmpeg include directory not found!
    goto :error
)

:: 清理下载文件
del "%FFMPEG_ZIP%"

echo.
echo ================================================
echo FFmpeg setup completed successfully!
echo ================================================
echo.
echo Directory structure:
echo   deps\ffmpeg\bin\     - DLL files and executables
echo   deps\ffmpeg\lib\     - Import libraries (.lib files)
echo   deps\ffmpeg\include\ - Header files
echo.
echo Available libraries:
dir /b ffmpeg\lib\*.lib 2>nul

echo.
echo FFmpeg version information:
ffmpeg\bin\ffmpeg.exe -version 2>nul | findstr "ffmpeg version"

cd ..
echo.
echo You can now run: xmake f --vs=2022 --vs_toolset=14.38
goto :end

:error
cd ..
echo.
echo ================================================
echo Setup failed! Please try again or:
echo 1. Check your internet connection
echo 2. Download FFmpeg manually from: https://ffmpeg.org/download.html#build-windows
echo 3. Extract to deps\ffmpeg directory
echo ================================================
exit /b 1

:end
echo.
echo Press any key to continue...
pause >nul