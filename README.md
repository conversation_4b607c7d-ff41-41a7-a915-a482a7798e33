# RTSP视频处理Demo - 不同VS2022工具链版本对比

这个项目包含三个RTSP视频处理的C++ demo，分别使用不同版本的VS2022工具链编译，展示了传统C++17和现代C++20编程风格的差异，以及FFmpeg和OpenCV两种不同库的使用方法。

## 项目结构

```
├── rtsp_demo_14_38/          # VS2022 Tools 14.38.33130 (C++17) - FFmpeg录制
│   ├── src/
│   │   ├── main.cpp
│   │   ├── rtsp_client.h
│   │   └── rtsp_client.cpp
│   ├── xmake.lua
│   ├── build.bat
│   └── README.md
├── rtsp_demo_14_44/          # VS2022 Tools 14.44.35207 (C++20) - FFmpeg录制
│   ├── src/
│   │   ├── main.cpp
│   │   ├── rtsp_client.h
│   │   └── rtsp_client.cpp
│   ├── xmake.lua
│   ├── build.bat
│   └── README.md
├── rtsp_opencv_demo_14_44/   # VS2022 Tools 14.44.35207 (C++20) - OpenCV截图
│   ├── src/
│   │   ├── main.cpp
│   │   ├── rtsp_capture.h
│   │   └── rtsp_capture.cpp
│   ├── xmake.lua
│   ├── build.bat
│   └── README.md
└── README.md                 # 本文件
```

## 功能特性

### 📹 视频录制Demo (FFmpeg)
- 连接RTSP视频流
- 实时拉取视频数据
- 保存完整视频到本地文件
- 支持多种输出格式（MP4, AVI, MKV, MOV）

### 📸 截图Demo (OpenCV)
- 连接RTSP视频流
- 定时自动截图（可配置间隔）
- 手动截图命令
- 高质量JPEG图片保存
- 实时状态监控和交互式控制

## 版本对比

### rtsp_demo_14_38 (VS2022 Tools 14.38.33130)
- **C++标准**: C++17
- **编程风格**: 传统C++
- **特点**:
  - 使用原始指针和手动内存管理
  - 传统的错误处理方式
  - 简单的字符串拼接
  - 基础的类设计

### rtsp_demo_14_44 (VS2022 Tools 14.44.35207) - FFmpeg录制
- **C++标准**: C++20
- **编程风格**: 现代C++
- **库**: FFmpeg
- **特点**:
  - 智能指针和RAII自动内存管理
  - std::format现代字符串格式化
  - 强类型枚举和std::optional
  - 移动语义支持
  - 回调函数机制
  - 更好的错误处理
  - std::chrono类型安全时间处理

### rtsp_opencv_demo_14_44 (VS2022 Tools 14.44.35207) - OpenCV截图
- **C++标准**: C++20
- **编程风格**: 现代C++
- **库**: OpenCV
- **特点**:
  - 定时自动截图功能
  - 交互式命令控制
  - 实时状态监控
  - 高质量图片保存
  - 线程安全的状态管理
  - 信号处理和优雅退出
  - std::filesystem文件系统操作

## 快速开始

### 前置要求
1. 安装Visual Studio 2022
2. 安装xmake构建工具
3. 确保系统中有FFmpeg库

### 编译和运行

#### 方法1: 使用批处理脚本
```bash
# 编译14.38版本 (FFmpeg录制)
cd rtsp_demo_14_38
build.bat

# 编译14.44版本 (FFmpeg录制)
cd rtsp_demo_14_44
build.bat

# 编译OpenCV截图版本
cd rtsp_opencv_demo_14_44
build.bat
```

#### 方法2: 手动编译
```bash
# 14.38版本 (FFmpeg录制)
cd rtsp_demo_14_38
xmake config --toolchain=msvc --vs_toolset=14.38.33130 --mode=release 
xmake build

# 或者一步完成配置和编译
xmake f --toolchain=msvc --vs_toolset=14.38.33130 --mode=release && xmake

# 14.44版本 (FFmpeg录制)
cd rtsp_demo_14_44
# 清理并编译
xmake clean
xmake config --toolchain=msvc --vs_toolset=14.44.35207
xmake build

# OpenCV截图版本
cd rtsp_opencv_demo_14_44

# 清理并编译
xmake clean
xmake config --toolchain=msvc --vs_toolset=14.44.35207
xmake build
```

### 运行示例

#### FFmpeg录制版本
```bash
# 使用命令行参数
bin\rtsp_demo.exe rtsp://example.com:554/stream output.mp4 30

# 交互式输入
bin\rtsp_demo.exe
```

#### OpenCV截图版本
```bash
# 使用命令行参数
bin\rtsp_opencv_demo.exe rtsp://example.com:554/stream screenshots 30

# 交互式输入
bin\rtsp_opencv_demo.exe

# 运行时命令
# s - 手动截图
# i - 显示信息
# q - 退出程序
```

## 测试RTSP流

### 公共测试流
```
rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4
```

### 本地摄像头
```
rtsp://admin:password@*************:554/h264
rtsp://your-camera-ip:554/stream
```

## 技术细节

### 依赖库
- **FFmpeg**: 用于视频编解码和格式处理
- **Windows Socket**: 网络通信
- **MSVC运行时**: 多线程DLL版本

### 支持的协议
- RTSP over TCP
- H.264/H.265视频编码
- 多种容器格式

### 性能特点
- 低延迟流处理


## 开发说明

### 添加新功能
1. 在对应版本的src目录下修改源码
2. 更新xmake.lua配置文件
3. 重新编译测试

### 调试技巧
1. 使用Visual Studio调试器
2. 启用FFmpeg日志输出
3. 检查网络连接状态

## 常见问题

### 编译问题
1. **工具链版本不匹配**: 确保安装了指定版本的VS2022工具集
2. **FFmpeg库缺失**: 通过xmake包管理器自动下载
3. **链接错误**: 检查运行时库设置

### 运行问题
1. **连接超时**: 检查RTSP URL和网络连接
2. **编码不支持**: 确保FFmpeg支持目标编码格式
3. **权限问题**: 某些摄像头需要认证信息

## 许可证

本项目仅用于学习和演示目的。
