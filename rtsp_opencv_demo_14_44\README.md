# RTSP OpenCV Screenshot Demo - VS2022 Tools 14.44.35207

这个demo使用VS2022工具链版本14.44.35207和OpenCV库，实现从RTSP视频流自动截图保存功能，展示现代C++20编程特性。

## 🎯 功能特性

### 核心功能
- **自动截图**: 每30秒（可配置）自动从RTSP流截图
- **手动截图**: 支持实时手动截图命令
- **高质量保存**: JPEG格式，95%质量保存
- **实时监控**: 显示连接状态、截图数量等信息
- **优雅退出**: 支持Ctrl+C信号处理

### 现代C++20特性
- **智能指针**: 自动内存管理和RAII
- **std::format**: 现代字符串格式化
- **强类型枚举**: 类型安全的状态管理
- **std::optional**: 安全的错误处理
- **std::filesystem**: 现代文件系统操作
- **std::atomic**: 线程安全的状态管理
- **std::chrono**: 类型安全的时间处理
- **回调函数**: 事件驱动的架构

## 🛠️ 依赖要求

- **VS2022 工具链**: 14.44.35207
- **OpenCV**: 4.x版本
- **xmake**: 构建系统
- **C++20**: 编译器支持

## 🚀 编译和运行

### 编译
```bash
# 进入项目目录
cd rtsp_opencv_demo_14_44

# 使用批处理脚本编译
build.bat

# 或手动编译
xmake config --toolchain=msvc --vs_toolset=14.44.35207
xmake build
```

### 运行
```bash
# 命令行参数方式
bin\rtsp_opencv_demo.exe <rtsp_url> [output_dir] [interval_seconds]

# 示例
bin\rtsp_opencv_demo.exe rtsp://example.com:554/stream screenshots 30

# 交互式输入
bin\rtsp_opencv_demo.exe
```

## 📋 使用说明

### 命令行参数
1. **rtsp_url**: RTSP流地址（必需）
2. **output_dir**: 截图保存目录（可选，默认：screenshots）
3. **interval_seconds**: 截图间隔秒数（可选，默认：30）

### 交互式命令
运行后可使用以下命令：
- **s** 或 **screenshot**: 立即截图
- **i** 或 **info**: 显示当前状态信息
- **q** 或 **quit**: 退出程序
- **Ctrl+C**: 优雅退出

### 输出文件格式
截图文件命名格式：`screenshot_YYYYMMDD_HHMMSS_mmm.jpg`
- 年月日时分秒 + 毫秒精度
- 高质量JPEG格式（95%质量）

## 📊 状态监控

### 实时状态
- **Disconnected**: 未连接
- **Connecting**: 连接中
- **Connected**: 已连接
- **Capturing**: 正在截图
- **Error**: 错误状态
- **Stopped**: 已停止

### 统计信息
- 截图总数
- 运行时间
- 平均截图频率
- 帧尺寸信息

## 🎬 示例RTSP流

### 公共测试流
```
rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4
```

### 本地摄像头
```
rtsp://admin:password@*************:554/h264
rtsp://your-camera-ip:554/stream
```

## 🔧 高级配置

### OpenCV参数优化
- 缓冲区大小：1帧（降低延迟）
- JPEG质量：95%（高质量保存）
- 线程安全：原子操作保护

### 性能特点
- **低延迟**: 最小缓冲区设置
- **高效率**: 智能线程管理
- **稳定性**: 异常处理和重连机制
- **资源管理**: RAII自动清理

## 🐛 故障排除

### 常见问题
1. **连接失败**
   - 检查RTSP URL格式
   - 验证网络连接
   - 确认摄像头支持TCP传输

2. **截图失败**
   - 检查输出目录权限
   - 确认磁盘空间充足
   - 验证OpenCV安装

3. **编译错误**
   - 确认VS2022工具集版本
   - 检查OpenCV库安装
   - 验证C++20支持

### 调试技巧
- 启用详细错误信息
- 检查回调函数输出
- 监控系统资源使用

## 📈 性能优化

### 建议设置
- **截图间隔**: 根据需求调整（建议≥10秒）
- **输出目录**: 使用SSD存储提高性能
- **网络**: 稳定的网络连接

### 资源使用
- **CPU**: 低占用率（智能休眠）
- **内存**: 最小化缓冲区
- **磁盘**: 按需写入

## 📝 开发说明

### 扩展功能
- 添加视频录制功能
- 支持多路RTSP流
- 实现Web界面监控
- 添加运动检测

### 代码结构
```
src/
├── main.cpp           # 主程序入口
├── rtsp_capture.h     # RTSP捕获类声明
└── rtsp_capture.cpp   # RTSP捕获类实现
```

## 📄 许可证

本项目仅用于学习和演示目的。
