{
    ["find_program_msvc_arch_x64_plat_windows_version_14.38.33130_checktoolld"] = {
        ["link.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    ["find_program_msvc_arch_x64_plat_windows_version_14.38.33130_checktoolcc"] = {
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    ["find_program_msvc_arch_x64_plat_windows_version_14.38.33130_checktoolcxx"] = {
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    find_package_windows_x64_fetch_package_system = {
        ffmpeg_9598f6f791f44c8cb248c15cb5c55a5d_release_external = false
    },
    find_program_fetch_package_xmake = {
        msys2 = false,
        nasm = false,
        ["msys2-base"] = false
    },
    ["detect.sdks.find_vcpkgdir"] = false,
    find_programver = {
        ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe"] = "19.44.35214",
        ["C:\\Users\\<USER>\\scoop\\apps\\xmake\\current\\winenv\\bin\\curl"] = "8.11.0"
    },
    find_program_fetch_package_system = {
        ["msys2-base"] = false,
        msys2 = false
    },
    find_package_windows_x64_fetch_package_xmake = {
        ["xmake::ffmpeg_9598f6f791f44c8cb248c15cb5c55a5d_release_7.1_external"] = false
    },
    find_program = {
        nim = false,
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]],
        git = [[C:\Program Files\Git\cmd\git.exe]],
        curl = [[C:\Users\<USER>\scoop\apps\xmake\current\winenv\bin\curl]],
        gzip = false,
        ["7z"] = [[C:\Users\<USER>\scoop\apps\xmake\current\winenv\bin\7z]]
    }
}