-- xmake.lua for RTSP demo - Pure Windows build (No MSYS2)
set_project("rtsp_demo_native_windows")
set_version("1.0.0")

-- 设置C++标准
set_languages("c++20")

-- 设置编译器工具链版本
set_toolchains("msvc", {version = "14.44.35207"})

-- 检查预编译FFmpeg是否存在
local ffmpeg_path = path.join(os.scriptdir(), "..", "ffmpeg_prebuilt", "ffmpeg_libs")

if os.exists(ffmpeg_path) then
    print("Using pre-compiled FFmpeg (No MSYS2 required)")
    
    -- 添加FFmpeg头文件路径
    add_includedirs(path.join(ffmpeg_path, "include"))
    
    -- 添加FFmpeg库路径
    add_linkdirs(path.join(ffmpeg_path, "lib"))
    
    -- 链接FFmpeg库
    add_links("avformat", "avcodec", "avutil", "swscale", "avfilter", "swresample")
    
    -- 复制DLL到输出目录的规则
    after_build(function (target)
        local dll_path = path.join(ffmpeg_path, "bin")
        local target_dir = target:targetdir()
        
        -- 复制必要的DLL文件
        local dlls = {
            "avformat*.dll",
            "avcodec*.dll", 
            "avutil*.dll",
            "swscale*.dll",
            "avfilter*.dll",
            "swresample*.dll"
        }
        
        for _, dll_pattern in ipairs(dlls) do
            os.cp(path.join(dll_path, dll_pattern), target_dir)
        end
        
        print("FFmpeg DLLs copied to output directory")
    end)
    
else
    print("Pre-compiled FFmpeg not found. Run setup_ffmpeg_windows.bat first!")
    print("Or falling back to package manager (may require MSYS2)...")
    
    -- 回退到包管理器，但尝试指定Windows原生构建
    add_requires("ffmpeg", {
        configs = {
            shared = true,
            toolchains = "msvc",
            vs_runtime = "MD"
        }
    })
    
    add_packages("ffmpeg")
end

-- 定义目标
target("rtsp_demo_native")
    set_kind("binary")
    add_files("src/*.cpp")
    add_headerfiles("src/*.h")
    
    -- 设置输出目录
    set_targetdir("bin")
    
    -- 添加编译选项
    add_cxxflags("/W4", "/std:c++20")
    add_defines("_CRT_SECURE_NO_WARNINGS")
    
    -- Windows系统库
    add_links("ws2_32", "secur32", "bcrypt", "mfplat", "mfuuid", "strmiids")
    
    -- 设置运行时库
    set_runtimes("MD")
    
    -- 启用现代C++特性
    add_cxxflags("/permissive-", "/Zc:__cplusplus")
    
    -- 优化设置
    if is_mode("release") then
        add_cxxflags("/O2", "/DNDEBUG")
        add_ldflags("/OPT:REF", "/OPT:ICF")
    end
