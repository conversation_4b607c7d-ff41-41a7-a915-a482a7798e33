@echo off
echo Building RTSP OpenCV Screenshot Demo with VS2022 Tools 14.44.35207 (C++20)...

REM 清理之前的构建
xmake clean

REM 配置项目，指定工具链版本
xmake config --toolchain=msvc --vs_toolset=14.44.35207 --mode=release

REM 编译项目
xmake build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful!
    echo Executable: bin\rtsp_opencv_demo.exe
    echo.
    echo Usage: bin\rtsp_opencv_demo.exe ^<rtsp_url^> [output_dir] [interval_seconds]
    echo Example: bin\rtsp_opencv_demo.exe rtsp://example.com:554/stream screenshots 30
    echo.
    echo Features:
    echo - Automatic screenshots every N seconds
    echo - Manual screenshot command 's'
    echo - Real-time status monitoring
    echo - Modern C++20 with OpenCV
    echo - Interactive commands: s(screenshot), i(info), q(quit)
) else (
    echo.
    echo Build failed!
    exit /b 1
)

pause
