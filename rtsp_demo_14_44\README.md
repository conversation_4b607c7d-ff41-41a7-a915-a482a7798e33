# RTSP Video Recorder Demo - VS2022 Tools 14.44.35207

这个demo使用VS2022工具链版本14.44.35207编译，展示如何从RTSP流拉取视频并保存到本地文件，使用现代C++20特性。

## 特性

- 支持RTSP流连接
- 视频录制和保存
- 使用FFmpeg库进行视频处理
- C++20标准
- 现代C++编程风格：
  - 智能指针和RAII
  - std::format格式化
  - 强类型枚举
  - std::optional
  - 移动语义
  - 回调函数支持
  - std::chrono时间处理

## 依赖

- VS2022 工具链 14.44.35207
- FFmpeg库
- xmake构建系统

## 编译

```bash
# 进入项目目录
cd rtsp_demo_14_44

# 配置项目
xmake config --toolchain=msvc --vs_toolset=14.44.35207

# 编译项目
xmake build

# 或者一步完成
xmake
```

## 使用方法

### 命令行参数
```bash
# 基本用法
./bin/rtsp_demo.exe <rtsp_url> <output_file> [duration_seconds]

# 示例
./bin/rtsp_demo.exe rtsp://example.com:554/stream output.mp4 60
```

### 交互式输入
如果不提供命令行参数，程序会提示输入：
```bash
./bin/rtsp_demo.exe
```

## 高级特性

### 进度回调
程序支持进度回调，实时显示录制进度：
```cpp
client.setProgressCallback([](int frameCount, std::chrono::seconds elapsed) {
    std::cout << std::format("Progress: {} frames, {}s elapsed", frameCount, elapsed.count()) << std::endl;
});
```

### 错误处理
支持详细的错误信息和回调：
```cpp
client.setErrorCallback([](const std::string& error) {
    std::cerr << std::format("RTSP Error: {}", error) << std::endl;
});
```

### 连接状态监控
可以查询连接状态：
```cpp
auto status = client.getStatus();
auto lastError = client.getLastError();
```

## 示例RTSP URL

```
rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4
rtsp://your-camera-ip:554/stream
rtsp://admin:password@*************:554/h264
```

## 输出格式

支持的输出格式：
- MP4 (.mp4)
- AVI (.avi)
- MKV (.mkv)
- MOV (.mov)

## C++20特性使用

1. **std::format**: 现代字符串格式化
2. **智能指针**: 自动内存管理
3. **强类型枚举**: 类型安全的枚举
4. **std::optional**: 可选值处理
5. **移动语义**: 高效的资源转移
6. **std::chrono**: 类型安全的时间处理
7. **std::function**: 回调函数支持

## 注意事项

1. 需要支持C++20的编译器
2. 确保网络连接正常
3. RTSP服务器需要支持TCP传输
4. 某些摄像头可能需要认证信息
5. 录制时间建议不要过长，避免文件过大

## 编译器版本信息

- MSVC工具集：14.44.35207
- C++标准：C++20
- 运行时库：MD (多线程DLL)
- 编译选项：/permissive- /Zc:__cplusplus
