#include <iostream>
#include <string>
#include <format>
#include <chrono>
#include "rtsp_client.h"

int main(int argc, char* argv[]) {
    std::cout << "=== RTSP Video Recorder Demo (VS2022 Tools 14.44.35207 - C++20) ===" << std::endl;
    
    std::string rtspUrl;
    std::string outputFile;
    std::chrono::seconds duration{30};

    // 检查命令行参数
    if (argc >= 2) {
        rtspUrl = argv[1];
    } else {
        std::cout << "Enter RTSP URL: ";
        std::getline(std::cin, rtspUrl);
    }

    if (argc >= 3) {
        outputFile = argv[2];
    } else {
        std::cout << "Enter output file name (e.g., output.mp4): ";
        std::getline(std::cin, outputFile);
    }

    if (argc >= 4) {
        duration = std::chrono::seconds{std::stoi(argv[3])};
    } else {
        std::cout << "Enter recording duration in seconds (default 30): ";
        std::string durationStr;
        std::getline(std::cin, durationStr);
        if (!durationStr.empty()) {
            duration = std::chrono::seconds{std::stoi(durationStr)};
        }
    }

    // 创建RTSP客户端
    RTSPClient client;
    
    // 设置回调函数 (使用C++20 lambda特性)
    client.setProgressCallback([](int frameCount, std::chrono::seconds elapsed) {
        std::cout << std::format("Progress: {} frames, {}s elapsed", frameCount, elapsed.count()) << std::endl;
    });
    
    client.setErrorCallback([](const std::string& error) {
        std::cerr << std::format("RTSP Error: {}", error) << std::endl;
    });

    // 初始化
    if (!client.initialize()) {
        std::cerr << "Failed to initialize RTSP client" << std::endl;
        if (const auto error = client.getLastError()) {
            std::cerr << std::format("Last error: {}", *error) << std::endl;
        }
        return -1;
    }

    // 打开RTSP流
    std::cout << std::format("Connecting to RTSP stream: {}", rtspUrl) << std::endl;
    if (!client.openStream(rtspUrl)) {
        std::cerr << "Failed to open RTSP stream" << std::endl;
        if (const auto error = client.getLastError()) {
            std::cerr << std::format("Last error: {}", *error) << std::endl;
        }
        return -1;
    }

    // 检查连接状态
    if (client.getStatus() != RTSPClient::ConnectionStatus::Connected) {
        std::cerr << "RTSP client not in connected state" << std::endl;
        return -1;
    }

    // 保存视频到文件
    std::cout << "Starting video recording..." << std::endl;
    const auto startTime = std::chrono::steady_clock::now();
    
    if (!client.saveToFile(outputFile, duration)) {
        std::cerr << "Failed to save video" << std::endl;
        if (const auto error = client.getLastError()) {
            std::cerr << std::format("Last error: {}", *error) << std::endl;
        }
        return -1;
    }

    const auto endTime = std::chrono::steady_clock::now();
    const auto totalTime = std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime);

    std::cout << "Video recording completed successfully!" << std::endl;
    std::cout << std::format("Output file: {}", outputFile) << std::endl;
    std::cout << std::format("Total recording time: {}s", totalTime.count()) << std::endl;
    
    return 0;
}
