#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <functional>
#include <optional>

// FFmpeg头文件 - Windows原生编译版本
extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/avutil.h>
#include <libavutil/imgutils.h>
#include <libswscale/swscale.h>
}

// 纯Windows构建的RTSP客户端
class RTSPClientNative {
public:
    // 回调函数类型
    using ProgressCallback = std::function<void(int frameCount, std::chrono::seconds elapsed)>;
    using ErrorCallback = std::function<void(const std::string& error)>;

    RTSPClientNative();
    ~RTSPClientNative();

    // 禁用拷贝
    RTSPClientNative(const RTSPClientNative&) = delete;
    RTSPClientNative& operator=(const RTSPClientNative&) = delete;

    // 支持移动
    RTSPClientNative(RTSPClientNative&& other) noexcept;
    RTSPClientNative& operator=(RTSPClientNative&& other) noexcept;

    [[nodiscard]] bool initialize();
    [[nodiscard]] bool openStream(const std::string& rtspUrl);
    [[nodiscard]] bool saveToFile(const std::string& outputFile, 
                                  std::chrono::seconds duration = std::chrono::seconds{30});
    
    void setProgressCallback(ProgressCallback callback) { m_progressCallback = std::move(callback); }
    void setErrorCallback(ErrorCallback callback) { m_errorCallback = std::move(callback); }
    
    void cleanup();

private:
    std::unique_ptr<AVFormatContext, void(*)(AVFormatContext*)> m_formatContext;
    std::unique_ptr<AVCodecContext, void(*)(AVCodecContext*)> m_codecContext;
    const AVCodec* m_codec;
    int m_videoStreamIndex;
    bool m_initialized;
    
    ProgressCallback m_progressCallback;
    ErrorCallback m_errorCallback;

    [[nodiscard]] bool findVideoStream();
    [[nodiscard]] bool setupDecoder();
    void setError(const std::string& error);
    void reportProgress(int frameCount, std::chrono::seconds elapsed);
};
