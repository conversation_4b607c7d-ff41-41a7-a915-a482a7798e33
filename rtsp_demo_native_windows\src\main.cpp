#include <iostream>
#include <string>
#include <format>
#include <chrono>
#include "rtsp_client_native.h"

int main(int argc, char* argv[]) {
    std::cout << "=== RTSP Demo - Pure Windows Build (No MSYS2) ===" << std::endl;
    std::cout << "Using pre-compiled FFmpeg libraries" << std::endl;
    
    std::string rtspUrl;
    std::string outputFile;
    std::chrono::seconds duration{30};

    // 检查命令行参数
    if (argc >= 2) {
        rtspUrl = argv[1];
    } else {
        std::cout << "Enter RTSP URL: ";
        std::getline(std::cin, rtspUrl);
    }

    if (argc >= 3) {
        outputFile = argv[2];
    } else {
        std::cout << "Enter output file name (e.g., output.mp4): ";
        std::getline(std::cin, outputFile);
    }

    if (argc >= 4) {
        duration = std::chrono::seconds{std::stoi(argv[3])};
    } else {
        std::cout << "Enter recording duration in seconds (default 30): ";
        std::string durationStr;
        std::getline(std::cin, durationStr);
        if (!durationStr.empty()) {
            duration = std::chrono::seconds{std::stoi(durationStr)};
        }
    }

    // 创建RTSP客户端
    RTSPClientNative client;
    
    // 设置回调函数
    client.setProgressCallback([](int frameCount, std::chrono::seconds elapsed) {
        std::cout << std::format("📹 Progress: {} frames, {}s elapsed", frameCount, elapsed.count()) << std::endl;
    });
    
    client.setErrorCallback([](const std::string& error) {
        std::cerr << std::format("❌ Error: {}", error) << std::endl;
    });

    // 初始化
    if (!client.initialize()) {
        std::cerr << "❌ Failed to initialize RTSP client" << std::endl;
        return -1;
    }

    // 打开RTSP流
    std::cout << std::format("🔗 Connecting to: {}", rtspUrl) << std::endl;
    if (!client.openStream(rtspUrl)) {
        std::cerr << "❌ Failed to open RTSP stream" << std::endl;
        return -1;
    }

    // 保存视频到文件
    std::cout << std::format("🎬 Recording to: {} ({}s)", outputFile, duration.count()) << std::endl;
    if (!client.saveToFile(outputFile, duration)) {
        std::cerr << "❌ Failed to save video" << std::endl;
        return -1;
    }

    std::cout << "✅ Recording completed successfully!" << std::endl;
    return 0;
}
