# RTSP Video Recorder Demo - VS2022 Tools 14.38.33130

这个demo使用VS2022工具链版本14.38.33130编译，展示如何从RTSP流拉取视频并保存到本地文件。

## 特性

- 支持RTSP流连接
- 视频录制和保存
- 使用FFmpeg库进行视频处理
- C++17标准
- 传统的C++编程风格

## 依赖

- VS2022 工具链 14.38.33130
- FFmpeg库
- xmake构建系统

## 编译

```bash
# 进入项目目录
cd rtsp_demo_14_38

# 配置项目
xmake config --toolchain=msvc --vs_toolset=14.38.33130

# 编译项目
xmake build

# 或者一步完成
xmake
```

## 使用方法

### 命令行参数
```bash
# 基本用法
./bin/rtsp_demo.exe <rtsp_url> <output_file> [duration_seconds]

# 示例
./bin/rtsp_demo.exe rtsp://example.com:554/stream output.mp4 60
```

### 交互式输入
如果不提供命令行参数，程序会提示输入：
```bash
./bin/rtsp_demo.exe
```

## 示例RTSP URL

```
rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4
rtsp://your-camera-ip:554/stream
rtsp://admin:password@*************:554/h264
```

## 输出格式

支持的输出格式：
- MP4 (.mp4)
- AVI (.avi)
- MKV (.mkv)
- MOV (.mov)

## 注意事项

1. 确保网络连接正常
2. RTSP服务器需要支持TCP传输
3. 某些摄像头可能需要认证信息
4. 录制时间建议不要过长，避免文件过大

## 编译器版本信息

- MSVC工具集：14.38.33130
- C++标准：C++17
- 运行时库：MD (多线程DLL)
