@echo off
echo Setting up FFmpeg for Windows (No MSYS2 required)...

REM 创建FFmpeg目录
if not exist "ffmpeg_prebuilt" mkdir ffmpeg_prebuilt
cd ffmpeg_prebuilt

echo.
echo Downloading pre-compiled FFmpeg for Windows...
echo This will download FFmpeg libraries compiled with MSVC (no MSYS2 needed)

REM 下载预编译的FFmpeg开发库
echo Downloading FFmpeg development libraries...
curl -L -o ffmpeg-dev.zip "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl-shared.zip"

if %ERRORLEVEL% NEQ 0 (
    echo Failed to download FFmpeg. Please check your internet connection.
    echo Alternative: Download manually from https://github.com/BtbN/FFmpeg-Builds/releases
    pause
    exit /b 1
)

echo.
echo Extracting FFmpeg...
tar -xf ffmpeg-dev.zip
if %ERRORLEVEL% NEQ 0 (
    echo Failed to extract. Trying with PowerShell...
    powershell -command "Expand-Archive -Path 'ffmpeg-dev.zip' -DestinationPath '.'"
)

REM 重命名目录
for /d %%i in (ffmpeg-master-*) do (
    move "%%i" ffmpeg_libs
    break
)

echo.
echo FFmpeg setup completed!
echo.
echo Directory structure:
echo ffmpeg_prebuilt/
echo   └── ffmpeg_libs/
echo       ├── bin/     (DLL files)
echo       ├── include/ (Header files)
echo       └── lib/     (Import libraries)
echo.
echo Now you can build your project without MSYS2!

cd ..
pause
