-- xmake.lua for RTSP OpenCV demo with VS2022 tools 14.44.35207
set_project("rtsp_opencv_demo_14_44")
set_version("1.0.0")

-- 设置C++标准
set_languages("c++20")

-- 设置编译器工具链版本
set_toolchains("msvc", {version = "14.44.35207"})

-- 使用本地OpenCV安装
local opencv_root = "F:/opencv_cpu_install"

if os.exists(opencv_root) then
    print("Using local OpenCV installation: " .. opencv_root)

    -- 添加OpenCV头文件路径
    add_includedirs(path.join(opencv_root, "include"))

    -- 添加OpenCV库路径
    add_linkdirs(path.join(opencv_root, "x64/vc16/lib"))  -- 根据你的OpenCV版本调整

    -- 链接OpenCV库 (Debug和Release版本)
    add_links("opencv_world4100", "opencv_world4100d")  -- 根据你的OpenCV版本调整版本号

    -- 复制OpenCV DLL到输出目录
    after_build(function (target)
        local dll_path = path.join(opencv_root, "x64/vc16/bin")  -- 根据你的OpenCV版本调整
        local target_dir = target:targetdir()

        -- 复制必要的DLL文件
        local dlls = {
            "opencv_world4100.dll",    -- Release版本
            "opencv_world4100d.dll"    -- Debug版本
        }

        for _, dll in ipairs(dlls) do
            local dll_file = path.join(dll_path, dll)
            if os.exists(dll_file) then
                os.cp(dll_file, target_dir)
                print("Copied: " .. dll)
            end
        end
    end)

else
    print("Local OpenCV not found, using package manager...")
    -- 回退到包管理器
    add_requires("opencv", {configs = {shared = true}})
    add_packages("opencv")
end

-- 定义目标
target("rtsp_opencv_demo")
    set_kind("binary")
    add_files("src/*.cpp")
    add_headerfiles("src/*.h")
    add_packages("opencv")
    
    -- 设置输出目录
    set_targetdir("bin")
    
    -- 添加编译选项
    add_cxxflags("/W4", "/std:c++20")
    add_defines("_CRT_SECURE_NO_WARNINGS")
    
    -- 链接库
    add_links("ws2_32")
    
    -- 设置运行时库
    set_runtimes("MD")
    
    -- 启用现代C++特性
    add_cxxflags("/permissive-", "/Zc:__cplusplus")
