{
    nasm_arch_x64_plat_windows = {
        __global = true,
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    cuda_arch_x64_plat_windows = {
        __global = true,
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    ["tool_package_table: 000001EA29C73450_windows_x64_cc"] = {
        toolname = "cl",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]],
        toolchain_info = {
            cachekey = "msvc_arch_x64_plat_windows_version_14.38.33130",
            plat = "windows",
            name = "msvc",
            arch = "x64"
        }
    },
    ["msvc_arch_x64_plat_windows_version_14.38.33130"] = {
        vs_toolset = "14.44.35207",
        arch = "x64",
        vs = "2022",
        vcarchs = {
            "arm",
            "arm64",
            "arm64ec",
            "x64",
            "x86"
        },
        vs_sdkver = "10.0.26100.0",
        vcvars = ref("msvc_arch_x64_plat_windows", "vcvars"),
        __checked = "2022",
        version = "14.38.33130",
        plat = "windows"
    },
    go_arch_x64_plat_windows = {
        __global = true,
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    yasm_arch_x64_plat_windows = {
        __global = true,
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    ["tool_package_table: 000001EA29C73450_windows_x64_ld"] = {
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]],
        toolchain_info = {
            cachekey = "msvc_arch_x64_plat_windows_version_14.38.33130",
            plat = "windows",
            name = "msvc",
            arch = "x64"
        }
    },
    ["tool_package_table: 000001EA29C73450_windows_x64_cxx"] = {
        toolname = "cl",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]],
        toolchain_info = {
            cachekey = "msvc_arch_x64_plat_windows_version_14.38.33130",
            plat = "windows",
            name = "msvc",
            arch = "x64"
        }
    },
    rust_arch_x64_plat_windows = {
        __global = true,
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    swift_arch_x64_plat_windows = {
        __global = true,
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    gfortran_arch_x64_plat_windows = {
        __global = true,
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    msvc_arch_x64_plat_windows = {
        vs_toolset = "14.44.35207",
        arch = "x64",
        vs = "2022",
        vcarchs = {
            "arm",
            "arm64",
            "arm64ec",
            "x64",
            "x86"
        },
        __global = true,
        vcvars = {
            PATH = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\VCPackages;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TeamFoundation\Team Explorer;C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\bin\Roslyn;C:\Program Files\Microsoft Visual Studio\2022\Community\Team Tools\DiagnosticsHub\Collector;C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\\x64;C:\Program Files (x86)\Windows Kits\10\bin\\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\;D:\miniconda;D:\miniconda\Library\mingw-w64\bin;D:\miniconda\Library\usr\bin;D:\miniconda\Library\bin;D:\miniconda\Scripts;D:\miniconda\bin;D:\miniconda\condabin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\libnvvp;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\NVIDIA Corporation\Nsight Compute 2023.1.0;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin;C:\Program Files\Git\cmd;d:\cursor\resources\app\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;D:\PostgreSQL\16\bin;G:\cmake-3.31.6-windows-x86_64\bin;F:\TensorRT-*******.Windows10.x86_64.cuda-12.0\TensorRT-*******\lib;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;d:\Trae\bin;D:\miniconda;D:\miniconda\Library\mingw-w64\bin;D:\miniconda\Library\usr\bin;D:\miniconda\Library\bin;D:\miniconda\Scripts;C:\Users\<USER>\.local\bin;C:\Users\<USER>\scoop\apps\nodejs\current\bin;C:\Users\<USER>\scoop\apps\nodejs\current;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\cursor\resources\app\bin;D:\Microsoft VS Code\bin;D:\PyCharm 2025.1\bin;D:\CLion 2025.1\bin;D:\DataGrip 2025.1\bin;D:\Kiro\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\ajeetdsouza.zoxide_Microsoft.Winget.Source_8wekyb3d8bbwe;.;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\Linux\bin\ConnectionManagerExe;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\vcpkg]],
            VSCMD_ARG_app_plat = "Desktop",
            LIBPATH = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x86\store\references;C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.26100.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0;C:\Windows\Microsoft.NET\Framework64\v4.0.30319]],
            UCRTVersion = "10.0.26100.0",
            VisualStudioVersion = "17.0",
            WindowsSdkBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\]],
            VS170COMNTOOLS = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\]],
            LIB = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64;C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64;C:\Program Files (x86)\Windows Kits\10\\lib\10.0.26100.0\\um\x64]],
            WindowsSDKVersion = "10.0.26100.0",
            VSInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\]],
            WindowsLibPath = [[C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.26100.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0]],
            VCToolsInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\]],
            VCIDEInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\]],
            ExtensionSdkDir = [[C:\Program Files (x86)\Microsoft SDKs\Windows Kits\10\ExtensionSDKs]],
            VSCMD_ARG_TGT_ARCH = "x64",
            VCToolsVersion = "14.44.35207",
            WindowsSdkVerBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\]],
            VSCMD_ARG_HOST_ARCH = "x64",
            DevEnvdir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\]],
            UniversalCRTSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]],
            INCLUDE = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt]],
            VCInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\]],
            VSCMD_VER = "17.14.11",
            WindowsSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]],
            VCToolsRedistDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Redist\MSVC\14.44.35112\]]
        },
        vs_sdkver = "10.0.26100.0",
        __checked = "2022",
        plat = "windows"
    },
    fpc_arch_x64_plat_windows = {
        __global = true,
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    nim_arch_x64_plat_windows = {
        __global = true,
        plat = "windows",
        arch = "x64",
        __checked = false
    }
}