# RTSP Demo - Pure Windows Build (No MSYS2)

这个demo展示如何在Windows上直接构建RTSP视频处理应用，**完全避免MSYS2依赖**，使用预编译的FFmpeg库。

## 🎯 为什么选择纯Windows构建？

### ❌ MSYS2的问题
- **体积庞大**: 下载几GB的Unix模拟环境
- **构建复杂**: 需要额外的工具链和依赖
- **性能开销**: Unix模拟层的性能损失
- **维护困难**: 多套工具链的版本管理

### ✅ 纯Windows构建的优势
- **轻量级**: 只下载必需的FFmpeg库文件
- **原生性能**: 直接使用Windows/MSVC工具链
- **简单维护**: 单一工具链，版本管理简单
- **快速构建**: 无需编译FFmpeg源码

## 🚀 快速开始

### 步骤1: 下载预编译FFmpeg
```bash
# 运行自动下载脚本
setup_ffmpeg_windows.bat
```

这将下载预编译的FFmpeg库到 `ffmpeg_prebuilt/` 目录：
```
ffmpeg_prebuilt/
└── ffmpeg_libs/
    ├── bin/     # DLL文件
    ├── include/ # 头文件
    └── lib/     # 导入库
```

### 步骤2: 编译项目
```bash
cd rtsp_demo_native_windows
build_native.bat
```

### 步骤3: 运行
```bash
bin\rtsp_demo_native.exe rtsp://example.com:554/stream output.mp4 30
```

## 🔧 技术细节

### FFmpeg库来源
- **来源**: [FFmpeg-Builds](https://github.com/BtbN/FFmpeg-Builds)
- **编译器**: MSVC (与你的项目一致)
- **架构**: x64
- **链接方式**: 动态链接 (shared)
- **许可证**: GPL-3.0

### 构建配置
```lua
-- xmake.lua 关键配置
set_toolchains("msvc", {version = "14.44.35207"})

-- 检测预编译FFmpeg
if os.exists(ffmpeg_path) then
    add_includedirs(path.join(ffmpeg_path, "include"))
    add_linkdirs(path.join(ffmpeg_path, "lib"))
    add_links("avformat", "avcodec", "avutil", "swscale")
end
```

### 自动DLL复制
构建完成后，xmake会自动复制必要的DLL到输出目录：
- `avformat*.dll`
- `avcodec*.dll` 
- `avutil*.dll`
- `swscale*.dll`

## 📊 性能对比

| 构建方式 | 下载大小 | 构建时间 | 运行性能 | 维护复杂度 |
|---------|---------|---------|---------|-----------|
| MSYS2构建 | ~2GB | 长 | 中等 | 高 |
| 预编译库 | ~50MB | 短 | 最佳 | 低 |

## 🛠️ 手动配置（高级）

如果你有自己的FFmpeg库，可以手动配置：

1. **创建目录结构**:
```
your_ffmpeg/
├── include/
│   ├── libavformat/
│   ├── libavcodec/
│   └── libavutil/
├── lib/
│   ├── avformat.lib
│   ├── avcodec.lib
│   └── avutil.lib
└── bin/
    ├── avformat-XX.dll
    ├── avcodec-XX.dll
    └── avutil-XX.dll
```

2. **修改xmake.lua**:
```lua
local ffmpeg_path = "path/to/your_ffmpeg"
add_includedirs(path.join(ffmpeg_path, "include"))
add_linkdirs(path.join(ffmpeg_path, "lib"))
```

## 🔍 故障排除

### 常见问题

1. **"FFmpeg libraries not found"**
   ```bash
   # 解决方案：运行下载脚本
   setup_ffmpeg_windows.bat
   ```

2. **"DLL not found"错误**
   ```bash
   # 检查DLL是否在bin目录
   dir bin\*.dll
   
   # 手动复制DLL
   copy ..\ffmpeg_prebuilt\ffmpeg_libs\bin\*.dll bin\
   ```

3. **链接错误**
   ```bash
   # 检查库文件
   dir ..\ffmpeg_prebuilt\ffmpeg_libs\lib\*.lib
   
   # 重新配置
   xmake config --recheck
   ```

### 调试技巧
```bash
# 查看详细构建信息
xmake build -v

# 检查链接的库
xmake show

# 清理重建
xmake clean --all && xmake
```

## 🎯 使用场景

### 适合的场景
- ✅ 生产环境部署
- ✅ 快速原型开发
- ✅ CI/CD自动构建
- ✅ 学习FFmpeg API

### 不适合的场景
- ❌ 需要自定义FFmpeg编译选项
- ❌ 需要特殊的编解码器
- ❌ 对库文件大小极其敏感

## 📈 扩展建议

1. **添加更多编解码器**: 下载包含更多编解码器的FFmpeg版本
2. **静态链接**: 使用静态库版本避免DLL依赖
3. **多架构支持**: 同时支持x86和x64
4. **版本管理**: 使用特定版本的FFmpeg库

## 📝 总结

这个纯Windows构建方案提供了：
- 🚀 **快速**: 无需编译FFmpeg源码
- 🎯 **简单**: 一键下载和构建
- 💪 **高效**: 原生Windows性能
- 🔧 **灵活**: 易于定制和扩展

完美适合需要快速开发和部署RTSP视频处理应用的场景！
