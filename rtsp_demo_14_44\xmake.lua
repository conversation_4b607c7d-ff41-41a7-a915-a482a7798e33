-- xmake.lua for RTSP demo with VS2022 tools 14.44.35207
set_project("rtsp_demo_14_44")
set_version("1.0.0")

-- 设置C++标准
set_languages("c++20")

-- 设置编译器工具链版本
set_toolchains("msvc", {version = "14.44.35207"})

-- 添加包管理器
add_requires("ffmpeg", {configs = {shared = true}})

-- 定义目标
target("rtsp_demo")
    set_kind("binary")
    add_files("src/*.cpp")
    add_headerfiles("src/*.h")
    add_packages("ffmpeg")
    
    -- 设置输出目录
    set_targetdir("bin")
    
    -- 添加编译选项
    add_cxxflags("/W4", "/std:c++20")
    add_defines("_CRT_SECURE_NO_WARNINGS")
    
    -- 链接库
    add_links("ws2_32", "secur32", "bcrypt")
    
    -- 设置运行时库
    set_runtimes("MD")
    
    -- 启用现代C++特性
    add_cxxflags("/permissive-", "/Zc:__cplusplus")
