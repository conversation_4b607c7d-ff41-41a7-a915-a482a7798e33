#pragma once

#include <string>
#include <memory>

extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/avutil.h>
#include <libavutil/imgutils.h>
#include <libswscale/swscale.h>
}

class RTSPClient {
public:
    RTSPClient();
    ~RTSPClient();

    bool initialize();
    bool openStream(const std::string& rtspUrl);
    bool saveToFile(const std::string& outputFile, int durationSeconds = 30);
    void cleanup();

private:
    AVFormatContext* m_formatContext;
    AVCodecContext* m_codecContext;
    AVCodec* m_codec;
    int m_videoStreamIndex;
    bool m_initialized;

    bool findVideoStream();
    bool setupDecoder();
};
