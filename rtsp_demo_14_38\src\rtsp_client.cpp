#include "rtsp_client.h"
#include <iostream>
#include <chrono>
#include <thread>

RTSPClient::RTSPClient()
    : m_formatContext(nullptr)
    , m_codecContext(nullptr)
    , m_codec(nullptr)
    , m_videoStreamIndex(-1)
    , m_initialized(false) {
}

RTSPClient::~RTSPClient() {
    cleanup();
}

bool RTSPClient::initialize() {
    if (m_initialized) {
        return true;
    }

    // 初始化FFmpeg (注意：新版本FFmpeg不需要av_register_all)
    #if LIBAVFORMAT_VERSION_INT < AV_VERSION_INT(58, 9, 100)
    av_register_all();
    #endif
    avformat_network_init();

    m_initialized = true;
    std::cout << "FFmpeg initialized successfully" << std::endl;
    return true;
}

bool RTSPClient::openStream(const std::string& rtspUrl) {
    if (!m_initialized) {
        std::cerr << "RTSPClient not initialized" << std::endl;
        return false;
    }

    // 分配格式上下文
    m_formatContext = avformat_alloc_context();
    if (!m_formatContext) {
        std::cerr << "Failed to allocate format context" << std::endl;
        return false;
    }

    // 设置RTSP选项
    AVDictionary* options = nullptr;
    av_dict_set(&options, "rtsp_transport", "tcp", 0);
    av_dict_set(&options, "stimeout", "5000000", 0); // 5秒超时

    // 打开输入流
    int ret = avformat_open_input(&m_formatContext, rtspUrl.c_str(), nullptr, &options);
    av_dict_free(&options);
    
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        std::cerr << "Failed to open RTSP stream: " << errbuf << std::endl;
        return false;
    }

    // 获取流信息
    ret = avformat_find_stream_info(m_formatContext, nullptr);
    if (ret < 0) {
        std::cerr << "Failed to find stream info" << std::endl;
        return false;
    }

    if (!findVideoStream() || !setupDecoder()) {
        return false;
    }

    std::cout << "RTSP stream opened successfully: " << rtspUrl << std::endl;
    return true;
}

bool RTSPClient::findVideoStream() {
    for (unsigned int i = 0; i < m_formatContext->nb_streams; i++) {
        if (m_formatContext->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            m_videoStreamIndex = i;
            break;
        }
    }

    if (m_videoStreamIndex == -1) {
        std::cerr << "No video stream found" << std::endl;
        return false;
    }

    return true;
}

bool RTSPClient::setupDecoder() {
    AVCodecParameters* codecpar = m_formatContext->streams[m_videoStreamIndex]->codecpar;
    
    // 查找解码器
    m_codec = avcodec_find_decoder(codecpar->codec_id);
    if (!m_codec) {
        std::cerr << "Codec not found" << std::endl;
        return false;
    }

    // 分配解码器上下文
    m_codecContext = avcodec_alloc_context3(m_codec);
    if (!m_codecContext) {
        std::cerr << "Failed to allocate codec context" << std::endl;
        return false;
    }

    // 复制参数
    int ret = avcodec_parameters_to_context(m_codecContext, codecpar);
    if (ret < 0) {
        std::cerr << "Failed to copy codec parameters" << std::endl;
        return false;
    }

    // 打开解码器
    ret = avcodec_open2(m_codecContext, m_codec, nullptr);
    if (ret < 0) {
        std::cerr << "Failed to open codec" << std::endl;
        return false;
    }

    return true;
}

bool RTSPClient::saveToFile(const std::string& outputFile, int durationSeconds) {
    if (!m_formatContext || m_videoStreamIndex == -1) {
        std::cerr << "Stream not opened" << std::endl;
        return false;
    }

    // 创建输出格式上下文
    AVFormatContext* outputContext = nullptr;
    int ret = avformat_alloc_output_context2(&outputContext, nullptr, nullptr, outputFile.c_str());
    if (ret < 0) {
        std::cerr << "Failed to create output context" << std::endl;
        return false;
    }

    // 添加视频流到输出
    AVStream* outStream = avformat_new_stream(outputContext, nullptr);
    if (!outStream) {
        std::cerr << "Failed to create output stream" << std::endl;
        avformat_free_context(outputContext);
        return false;
    }

    // 复制流参数
    ret = avcodec_parameters_copy(outStream->codecpar, 
                                  m_formatContext->streams[m_videoStreamIndex]->codecpar);
    if (ret < 0) {
        std::cerr << "Failed to copy stream parameters" << std::endl;
        avformat_free_context(outputContext);
        return false;
    }

    // 打开输出文件
    if (!(outputContext->oformat->flags & AVFMT_NOFILE)) {
        ret = avio_open(&outputContext->pb, outputFile.c_str(), AVIO_FLAG_WRITE);
        if (ret < 0) {
            std::cerr << "Failed to open output file" << std::endl;
            avformat_free_context(outputContext);
            return false;
        }
    }

    // 写入文件头
    ret = avformat_write_header(outputContext, nullptr);
    if (ret < 0) {
        std::cerr << "Failed to write header" << std::endl;
        if (!(outputContext->oformat->flags & AVFMT_NOFILE)) {
            avio_closep(&outputContext->pb);
        }
        avformat_free_context(outputContext);
        return false;
    }

    std::cout << "Starting to save video to: " << outputFile << std::endl;
    std::cout << "Duration: " << durationSeconds << " seconds" << std::endl;

    AVPacket packet;
    av_init_packet(&packet);
    
    auto startTime = std::chrono::steady_clock::now();
    int frameCount = 0;

    while (true) {
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(currentTime - startTime);
        
        if (elapsed.count() >= durationSeconds) {
            std::cout << "Recording duration reached" << std::endl;
            break;
        }

        ret = av_read_frame(m_formatContext, &packet);
        if (ret < 0) {
            if (ret == AVERROR_EOF) {
                std::cout << "End of stream reached" << std::endl;
            } else {
                std::cerr << "Error reading frame" << std::endl;
            }
            break;
        }

        if (packet.stream_index == m_videoStreamIndex) {
            // 重新计算时间戳
            packet.stream_index = 0;
            av_packet_rescale_ts(&packet, 
                                m_formatContext->streams[m_videoStreamIndex]->time_base,
                                outStream->time_base);

            ret = av_interleaved_write_frame(outputContext, &packet);
            if (ret < 0) {
                std::cerr << "Error writing frame" << std::endl;
                av_packet_unref(&packet);
                break;
            }
            frameCount++;
            
            if (frameCount % 30 == 0) {
                std::cout << "Frames written: " << frameCount << std::endl;
            }
        }

        av_packet_unref(&packet);
    }

    // 写入文件尾
    av_write_trailer(outputContext);

    // 清理
    if (!(outputContext->oformat->flags & AVFMT_NOFILE)) {
        avio_closep(&outputContext->pb);
    }
    avformat_free_context(outputContext);

    std::cout << "Video saved successfully. Total frames: " << frameCount << std::endl;
    return true;
}

void RTSPClient::cleanup() {
    if (m_codecContext) {
        avcodec_free_context(&m_codecContext);
        m_codecContext = nullptr;
    }

    if (m_formatContext) {
        avformat_close_input(&m_formatContext);
        m_formatContext = nullptr;
    }

    if (m_initialized) {
        avformat_network_deinit();
        m_initialized = false;
    }
}
