#include <iostream>
#include <string>
#include <format>
#include <chrono>
#include <thread>
#include <csignal>
#include <atomic>
#include "rtsp_capture.h"

using namespace rtsp_demo;

// 全局变量用于信号处理
std::atomic<bool> g_shouldExit{false};
std::unique_ptr<RTSPCapture> g_capture;

// 信号处理函数
void signalHandler(int signal) {
    std::cout << std::format("\nReceived signal {}, stopping capture...", signal) << std::endl;
    g_shouldExit = true;
    if (g_capture) {
        g_capture->stopCapture();
    }
}

// 状态转换为字符串
std::string statusToString(RTSPCapture::CaptureStatus status) {
    switch (status) {
        case RTSPCapture::CaptureStatus::Disconnected: return "Disconnected";
        case RTSPCapture::CaptureStatus::Connecting: return "Connecting";
        case RTSPCapture::CaptureStatus::Connected: return "Connected";
        case RTSPCapture::CaptureStatus::Capturing: return "Capturing";
        case RTSPCapture::CaptureStatus::Error: return "Error";
        case RTSPCapture::CaptureStatus::Stopped: return "Stopped";
        default: return "Unknown";
    }
}

int main(int argc, char* argv[]) {
    std::cout << "=== RTSP Screenshot Demo (VS2022 Tools 14.44.35207 - C++20 + OpenCV) ===" << std::endl;
    
    // 设置信号处理
    std::signal(SIGINT, signalHandler);
    std::signal(SIGTERM, signalHandler);
    
    std::string rtspUrl;
    std::string outputDir = "screenshots";
    int intervalSeconds = 30;

    // 解析命令行参数
    if (argc >= 2) {
        rtspUrl = argv[1];
    } else {
        std::cout << "Enter RTSP URL: ";
        std::getline(std::cin, rtspUrl);
    }

    if (argc >= 3) {
        outputDir = argv[2];
    } else {
        std::cout << std::format("Enter output directory (default: {}): ", outputDir);
        std::string input;
        std::getline(std::cin, input);
        if (!input.empty()) {
            outputDir = input;
        }
    }

    if (argc >= 4) {
        intervalSeconds = std::stoi(argv[3]);
    } else {
        std::cout << std::format("Enter screenshot interval in seconds (default: {}): ", intervalSeconds);
        std::string input;
        std::getline(std::cin, input);
        if (!input.empty()) {
            intervalSeconds = std::stoi(input);
        }
    }

    // 创建RTSP捕获对象
    g_capture = std::make_unique<RTSPCapture>();
    
    // 设置回调函数
    g_capture->setScreenshotCallback([](const RTSPCapture::ScreenshotInfo& info) {
        auto time_t = std::chrono::system_clock::to_time_t(info.timestamp);
        std::cout << std::format("📸 Screenshot saved: {} ({}x{}) at {:%Y-%m-%d %H:%M:%S}", 
                                info.filename, 
                                info.frameSize.width, 
                                info.frameSize.height,
                                std::chrono::system_clock::from_time_t(time_t)) << std::endl;
    });
    
    g_capture->setErrorCallback([](const std::string& error) {
        std::cerr << std::format("❌ RTSP Error: {}", error) << std::endl;
    });
    
    g_capture->setStatusCallback([](RTSPCapture::CaptureStatus status) {
        std::cout << std::format("🔄 Status changed to: {}", statusToString(status)) << std::endl;
    });

    // 初始化RTSP连接
    std::cout << std::format("🔗 Connecting to RTSP stream: {}", rtspUrl) << std::endl;
    if (!g_capture->initialize(rtspUrl)) {
        std::cerr << "❌ Failed to initialize RTSP capture" << std::endl;
        if (const auto error = g_capture->getLastError()) {
            std::cerr << std::format("Last error: {}", *error) << std::endl;
        }
        return -1;
    }

    // 开始截图捕获
    std::cout << std::format("📁 Output directory: {}", outputDir) << std::endl;
    std::cout << std::format("⏱️  Screenshot interval: {}s", intervalSeconds) << std::endl;
    
    if (!g_capture->startCapture(outputDir, std::chrono::seconds{intervalSeconds})) {
        std::cerr << "❌ Failed to start capture" << std::endl;
        if (const auto error = g_capture->getLastError()) {
            std::cerr << std::format("Last error: {}", *error) << std::endl;
        }
        return -1;
    }

    std::cout << "🚀 Capture started! Press Ctrl+C to stop..." << std::endl;
    std::cout << "Commands:" << std::endl;
    std::cout << "  - Press 's' + Enter to take manual screenshot" << std::endl;
    std::cout << "  - Press 'q' + Enter to quit" << std::endl;
    std::cout << "  - Press 'i' + Enter to show info" << std::endl;

    // 主循环
    std::string input;
    while (!g_shouldExit && g_capture->getStatus() == RTSPCapture::CaptureStatus::Capturing) {
        std::cout << "> ";
        if (std::getline(std::cin, input)) {
            if (input == "q" || input == "quit") {
                break;
            } else if (input == "s" || input == "screenshot") {
                std::cout << "📸 Taking manual screenshot..." << std::endl;
                if (g_capture->takeScreenshot()) {
                    std::cout << "✅ Manual screenshot saved" << std::endl;
                } else {
                    std::cout << "❌ Failed to take manual screenshot" << std::endl;
                }
            } else if (input == "i" || input == "info") {
                auto startTime = g_capture->getStartTime();
                auto now = std::chrono::system_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - startTime);
                
                std::cout << std::format("📊 Capture Info:") << std::endl;
                std::cout << std::format("   Status: {}", statusToString(g_capture->getStatus())) << std::endl;
                std::cout << std::format("   Screenshots taken: {}", g_capture->getScreenshotCount()) << std::endl;
                std::cout << std::format("   Running time: {}s", elapsed.count()) << std::endl;
                std::cout << std::format("   Output directory: {}", outputDir) << std::endl;
            } else if (!input.empty()) {
                std::cout << "❓ Unknown command. Available: s(screenshot), i(info), q(quit)" << std::endl;
            }
        }
        
        // 短暂休眠，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // 停止捕获
    std::cout << "🛑 Stopping capture..." << std::endl;
    g_capture->stopCapture();

    // 显示最终统计
    auto finalCount = g_capture->getScreenshotCount();
    auto startTime = g_capture->getStartTime();
    auto now = std::chrono::system_clock::now();
    auto totalTime = std::chrono::duration_cast<std::chrono::seconds>(now - startTime);
    
    std::cout << std::format("✅ Capture completed!") << std::endl;
    std::cout << std::format("   Total screenshots: {}", finalCount) << std::endl;
    std::cout << std::format("   Total time: {}s", totalTime.count()) << std::endl;
    std::cout << std::format("   Average rate: {:.2f} screenshots/minute", 
                            finalCount * 60.0 / totalTime.count()) << std::endl;

    return 0;
}
